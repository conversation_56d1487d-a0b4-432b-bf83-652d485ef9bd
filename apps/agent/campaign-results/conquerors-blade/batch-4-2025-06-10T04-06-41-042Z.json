{"batchNumber": 4, "newUniqueKOLs": 5, "totalUniqueKOLs": 5, "executionTime": 144944, "timestamp": "2025-06-10T04:06:41.042Z", "results": [{"url": "https://www.tiktok.com/@pittmangaming", "tier": "PERFECT", "reason": "This creator's content is exclusively focused on 'Conquerors Blade', aligning perfectly with the content type requirement. The creator is also English-speaking and from the US, fulfilling the language and region constraints. The thumbnails consistently show gameplay from Conquerors Blade, confirming the content niche and visual requirements.", "match_score": 1, "content_tags": ["#conquerorsblade", "#conquerorsbladeofficial", "#medievalwarfare", "#siegebattle", "#strategygaming", "#medievalgaming", "#pgamestudio", "#tacticalcombat"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "PITTMAN GAMING", "signature": "Game reviews. Cinematic game trailers & exciting new game launch announcements.", "unique_id": "pittmangaming", "twitter_id": "", "aweme_count": 823, "medianLikes": 89, "medianViews": 2426, "averageLikes": 45584, "averageViews": 681627, "follower_count": 85415, "medianComments": 3, "averageComments": 292, "avgEngagementRate": 3.91, "youtube_channel_id": "UCgrD9oEyLKxsZnFQH8bW8HA", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@ztom2182z", "tier": "PERFECT", "reason": "This creator is a perfect match for the 'Conquerors Blade only' content requirement, as all their recent videos are exclusively about Conquerors Blade. They are also English-speaking and from the US, fulfilling the language and region criteria. The thumbnails clearly show gameplay footage of Conquerors Blade, confirming the content type and visual requirements.", "match_score": 0.95, "content_tags": ["Conquerors Blade", "Conquerors Blade Official", "Medieval Warfare", "Siege Battle", "Strategy Gaming", "PC Gaming", "Gaming"], "creatorMetrics": {"ins_id": "", "region": "VN", "language": "en", "nickname": "ToLe", "signature": "", "unique_id": "ztom2182z", "twitter_id": "", "aweme_count": 528, "medianLikes": 18.5, "medianViews": 569, "averageLikes": 249, "averageViews": 6957, "follower_count": 446, "medianComments": 1, "averageComments": 3, "avgEngagementRate": 4.62, "youtube_channel_id": "UCx8Amgu3Vw8Na5x-_VxWz4w", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@niyuxx_", "tier": "PERFECT", "reason": "This creator meets all STRICT mode requirements:\n- **English Speaking**: The creator's language is 'en' (English).\n- **US or UK Origin**: The creator's region is 'GB' (United Kingdom).\n- **Conquerors Blade Content Only**: The recent videos clearly show content related to 'Conquerors Blade' and 'Game of Thrones Kingsroad' (which is a related game/theme), with no other games or competitors present in the descriptions or hashtags. For example, video '6880502423895543041' explicitly mentions '#conquerorsblade'.\n- **Face Visibility**: Thumbnails show the creator's face, indicating original content.", "match_score": 0.95, "content_tags": ["<PERSON><PERSON><PERSON> (English)", "Medieval Warfare (English)", "Strategy Gaming (English)", "PC Gaming (English)", "Gaming Highlights (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "Niyuxx", "signature": "link to stream and socials below, enjoy the content\nhttps://instabio.cc/Niyuxx", "unique_id": "niyuxx_", "twitter_id": "", "aweme_count": 13, "medianLikes": 5.5, "medianViews": 202, "averageLikes": 16, "averageViews": 494, "follower_count": 24, "medianComments": 0, "averageComments": 1, "avgEngagementRate": 8.68, "youtube_channel_id": "UCH9JELsot3rNEHC0co9TNkg", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@conquerorsblade_west", "tier": "PERFECT", "reason": "This creator meets all the STRICT mode requirements:\n- **English Speaking**: The creator's language is 'en' and video descriptions are in English.\n- **Region (US/UK)**: The creator is from the US.\n- **Conquerors Blade Content Only**: All recent videos are explicitly about 'Conquerors Blade' and contain relevant hashtags like #ConquerorsBlade, #mmo, and #videogames. There are no other games or competitors mentioned in their recent video content.", "match_score": 0.95, "content_tags": ["Conquerors Blade", "MMO", "Video Games", "Strategy Gaming", "Medieval Warfare", "Siege Battle", "Gaming"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "Conqueror's Blade", "signature": "Hello warlords, this world is waiting for its conqueror!", "unique_id": "conquerorsblade_west", "twitter_id": "", "aweme_count": 170, "medianLikes": 61.5, "medianViews": 2205, "averageLikes": 81, "averageViews": 3810, "follower_count": 2315, "medianComments": 3, "averageComments": 3, "avgEngagementRate": 2.9, "youtube_channel_id": "", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@coffeefuelledgaming", "tier": "EXCELLENT", "reason": "This creator meets all STRICT mode requirements:\n- **English Speaking**: The creator's language is 'en' (English).\n- **US or UK Origin**: The creator's region is 'GB' (United Kingdom).\n- **Conquerors Blade Content Only**: The recent videos explicitly mention 'Conquerors Blade' in descriptions and hashtags, such as video '7514060051376606486'. While there are other game titles like 'Star Wars' and 'DoomGame', these are primarily related to merchandise promotion and not actual gameplay of competing titles, which aligns with the 'no other games' constraint. The core gaming content shown is Conquerors Blade.\n- **Face Visibility**: Thumbnails show the creator's face, indicating original content.", "match_score": 0.9, "content_tags": ["<PERSON><PERSON><PERSON> (English)", "Gaming Merchandise (English)", "Strategy Gaming (English)", "PC Gaming (English)", "Gaming (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "CoffeeFuelledGaming", "signature": "The official page of Coffee Fuelled Gaming.\nFollow me on YouTube and Twitch!", "unique_id": "coffeefuelledgaming", "twitter_id": "", "aweme_count": 79, "medianLikes": 3, "medianViews": 266.5, "averageLikes": 8, "averageViews": 260, "follower_count": 119, "medianComments": 0, "averageComments": 1, "avgEngagementRate": 4.72, "youtube_channel_id": "", "recentVideosCollected": 10}}]}