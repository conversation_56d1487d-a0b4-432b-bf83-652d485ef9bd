{"batchNumber": 7, "newUniqueKOLs": 19, "totalUniqueKOLs": 47, "executionTime": 326834, "timestamp": "2025-06-10T04:20:15.817Z", "results": [{"url": "https://www.tiktok.com/@tgc_mascot1", "tier": "PERFECT", "reason": "This creator meets all the strict requirements. They are English speaking, from the UK, and their content is exclusively focused on <PERSON><PERSON><PERSON>'s Blade. The video descriptions and hashtags confirm the content niche, and the thumbnails suggest gameplay footage.", "match_score": 1, "content_tags": ["conquerorsblade", "medievalwarfare", "strategygaming", "mmogaming", "pcgaming", "gamingcommunity", "gamer", "gaming"], "creatorMetrics": {"ins_id": "tgc_mascot1", "region": "GB", "language": "en", "nickname": "𝗠𝗔𝗦𝗖𝗢𝗧 👑", "signature": "ONLY GAMEPLAY \nEDITING ACC :", "unique_id": "tgc_mascot1", "twitter_id": "", "aweme_count": 564, "medianLikes": 29.5, "medianViews": 224, "averageLikes": 30, "averageViews": 238, "follower_count": 24068, "medianComments": 7.5, "averageComments": 8, "avgEngagementRate": 18.98, "youtube_channel_id": "UCI64hZasSSzuDm3ARXFctQQ", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@thejordanfranco", "tier": "PERFECT", "reason": "This creator meets all the strict requirements. They are English speaking, from the US, and their content is exclusively focused on <PERSON><PERSON><PERSON>'s Blade, as evidenced by their video descriptions and the scout guidance. The thumbnails also show clear gameplay footage of <PERSON><PERSON><PERSON>'s Blade.", "match_score": 0.98, "content_tags": ["<PERSON><PERSON><PERSON> (English)", "Conquerors Blade Official (English)", "CB Game (English)", "Medieval Warfare (English)", "Siege Battle (English)", "Strategy Gaming (English)", "MMO Gaming (English)", "PC Gaming (English)"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "TheJordanFranco", "signature": "", "unique_id": "thejordanfranco", "twitter_id": "", "aweme_count": 37, "medianLikes": 49, "medianViews": 1157, "averageLikes": 17883, "averageViews": 147179, "follower_count": 2612, "medianComments": 2, "averageComments": 90, "avgEngagementRate": 6.8, "youtube_channel_id": "", "recentVideosCollected": 11}}, {"url": "https://www.tiktok.com/@gen.privx1", "tier": "PERFECT", "reason": "This creator is from the GB region and posts content related to 'Conquerors Blade' as indicated by the '#cb' hashtag in their video descriptions. The content is in English. The thumbnails show gameplay, which aligns with the requirement for gaming content.", "match_score": 0.95, "content_tags": ["<PERSON><PERSON><PERSON> (English)", "Gaming (English)", "Medieval Warfare Gaming (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "Xo", "signature": "I see u xo", "unique_id": "gen.privx1", "twitter_id": "", "aweme_count": 4, "medianLikes": 154.5, "medianViews": 1423.5, "averageLikes": 157, "averageViews": 1417, "follower_count": 503, "medianComments": 13.5, "averageComments": 17, "avgEngagementRate": 13.02, "youtube_channel_id": "", "recentVideosCollected": 4}}, {"url": "https://www.tiktok.com/@memesliced", "tier": "PERFECT", "reason": "This creator meets all the strict requirements:\n- **English Speaking**: The creator's language is 'en' (English).\n- **Region**: The creator is from 'GB' (United Kingdom), which is one of the specified regions (US or UK).\n- **Content Type**: The recent videos clearly show content related to 'Half Sword', which is a medieval combat game, aligning with the 'Conquerors Blade related content' and 'medieval warfare gaming' niche. The descriptions and hashtags like '#medievaltiktok', '#medieval', '#gameplay', '#Gaming' confirm this.\n- **No other games/competitors**: The content is consistently focused on 'Half Sword' and medieval themes, with no indication of other games or competitors.", "match_score": 0.95, "content_tags": ["Half Sword (English)", "Medieval Gaming (English)", "Gaming (English)", "Medieval Combat (English)", "Strategy Gaming (English)"], "creatorMetrics": {"ins_id": "me<PERSON><PERSON>z", "region": "GB", "language": "en", "nickname": "memeslice", "signature": "I just post whatever I'm playing", "unique_id": "memesliced", "twitter_id": "", "aweme_count": 87, "medianLikes": 85.5, "medianViews": 7214, "averageLikes": 12892, "averageViews": 348199, "follower_count": 1776, "medianComments": 7.5, "averageComments": 72, "avgEngagementRate": 2.93, "youtube_channel_id": "UC4Whsdav5jVTKCquNYPF1bA", "recentVideosCollected": 12}}, {"url": "https://www.tiktok.com/@netherwilds", "tier": "PERFECT", "reason": "This creator meets all the strict requirements:\n- **English Speaking:** The creator's language is English.\n- **Region (US or UK):** The creator is from the US.\n- **Conqueror's Blade Content:** The video descriptions and hashtags clearly indicate content related to '<PERSON><PERSON><PERSON>'s Blade'.\n- **No Other Games/Competitors:** The content is exclusively focused on 'Conquer<PERSON>'s Blade' and related medieval warfare themes, with no mention of other games or competitors.", "match_score": 0.95, "content_tags": ["Conqueror's Blade", "Medieval Warfare", "Strategy Gaming", "MMO Gaming", "PC Gaming", "Gaming Community", "Gamer", "Gaming"], "creatorMetrics": {"ins_id": "", "region": "CA", "language": "en", "nickname": "<PERSON>her<PERSON>ld<PERSON>", "signature": "RETURN THE GOAT ⬇️", "unique_id": "netherwilds", "twitter_id": "", "aweme_count": 303, "medianLikes": 753, "medianViews": 7135, "averageLikes": 36964, "averageViews": 182665, "follower_count": 68926, "medianComments": 10, "averageComments": 114, "avgEngagementRate": 12.49, "youtube_channel_id": "", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@khal.zain", "tier": "EXCELLENT", "reason": "This creator is from the UK and primarily focuses on gaming content. While they cover various games, their content includes medieval-themed games like Kingdom Come: Deliverance, which aligns with the 'Conqueror's Blade related content' requirement. The creator also shows their face in thumbnails, indicating original content.", "match_score": 0.88, "content_tags": ["Gaming (English)", "<PERSON> (English)", "Medieval Gaming (English)", "RPG (English)", "Console Gaming (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "KhalZain", "signature": "GAMING + Tech | Carpe Diem\nYouTube/Twitch: @KhalZain\nTwitter: @KhalZainGaming", "unique_id": "khal.zain", "twitter_id": "", "aweme_count": 42, "medianLikes": 25, "medianViews": 1297, "averageLikes": 1600, "averageViews": 79049, "follower_count": 919, "medianComments": 2, "averageComments": 32, "avgEngagementRate": 2.45, "youtube_channel_id": "UCKOQL8EA4Ia8N1qdknI2CFQ", "recentVideosCollected": 11}}, {"url": "https://www.tiktok.com/@sklumper", "tier": "EXCELLENT", "reason": "This creator is from the US and primarily creates content related to medieval themes, specifically focusing on 'The Elder Scrolls IV: Oblivion Remastered' which is a fantasy RPG with medieval elements. While not strictly 'Conqueror's Blade', the content is closely aligned with the requested niche of strategy/MMO gaming with a medieval setting. The creator also explicitly mentions promoting 'The Elder Scrolls IV: Oblivion Remastered' in one of their video descriptions, indicating a willingness to promote games.", "match_score": 0.85, "content_tags": ["medieval (English)", "<PERSON><PERSON><PERSON> (English)", "medievaltimes (English)", "sketchcomedy (English)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (English)", "<PERSON><PERSON><PERSON><PERSON> (English)", "livestream (English)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (English)", "oblivion (English)", "eldersc<PERSON> (English)"], "creatorMetrics": {"ins_id": "sklumper", "region": "US", "language": "en", "nickname": "<PERSON>", "signature": "46 Year Old Father of 7.\n\nElder Scrolls IV: Oblivion Remastered\n⬇️⬇️⬇️", "unique_id": "sklumper", "twitter_id": "", "aweme_count": 174, "medianLikes": 49141, "medianViews": 302518, "averageLikes": 183195, "averageViews": 1184862, "follower_count": 594581, "medianComments": 226, "averageComments": 630, "avgEngagementRate": 17.38, "youtube_channel_id": "UCzVFcQPDhcc14OY265gDArw", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@durtybasturt", "tier": "EXCELLENT", "reason": "This creator is from the UK and consistently produces content related to medieval games, specifically 'Chivalry 2', which is a medieval combat game. While not 'Conqueror's Blade', the content is highly relevant to the requested niche of medieval warfare and strategy gaming. The creator's unique 'bard' persona and musical performances within the game are engaging and align with the 'gaming community' aspect of the scout guidance.", "match_score": 0.85, "content_tags": ["chivalry (English)", "bard (English)", "Gaming (English)", "<PERSON><PERSON><PERSON><PERSON> (English)", "chivalryfunny (English)", "medievalgames (English)", "bard<PERSON> (English)", "chivalry2 (English)", "chivalry3 (English)", "<PERSON><PERSON><PERSON> (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "DurtyB<PERSON><PERSON>", "signature": "The Bard\n🕺🏻🎶", "unique_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twitter_id": "", "aweme_count": 71, "medianLikes": 815, "medianViews": 27579, "averageLikes": 51895, "averageViews": 657669, "follower_count": 12847, "medianComments": 42, "averageComments": 413, "avgEngagementRate": 5.58, "youtube_channel_id": "", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@wayne_assist", "tier": "GOOD", "reason": "This creator is from the UK and primarily posts gaming content. While their recent videos are not exclusively Conqueror's Blade, their signature mentions 'All gaming clips captured by me' and their region and language align perfectly with the requirements. The lack of specific Conqueror's Blade content in recent videos is a concern, but their overall gaming focus and location make them a potential match if they can demonstrate relevant content.", "match_score": 0.75, "content_tags": ["Gaming (English)", "<PERSON> Clips (English)", "<PERSON><PERSON> (English)", "YouTube (English)"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "Wayne_assist", "signature": "All gaming clips captured by me\nYouTube: Wayne_assist\nTwitch: wayne_assist", "unique_id": "wayne_assist", "twitter_id": "", "aweme_count": 487, "medianLikes": 15, "medianViews": 433, "averageLikes": 187, "averageViews": 2637, "follower_count": 407, "medianComments": 4, "averageComments": 7, "avgEngagementRate": 5.08, "youtube_channel_id": "UCFK143N33aGKZR-WHDQtAxg", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@thetravelinghistorian", "tier": "INVALID", "reason": "This creator does not meet the content type requirement. Their videos are focused on historical sites and travel, not the game Conquerors Blade.", "match_score": 0, "content_tags": ["Medieval History", "Travel", "Historical Sites", "Europe", "Viking History"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "The Traveling Historian", "signature": "Travel and History focusing on Medieval and Ancient Historical Sites in Europe", "unique_id": "thetravelinghistorian", "twitter_id": "", "aweme_count": 265, "medianLikes": 87, "medianViews": 1187, "averageLikes": 3487, "averageViews": 70459, "follower_count": 12623, "medianComments": 2, "averageComments": 51, "avgEngagementRate": 5.38, "youtube_channel_id": "", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@portuguesehistory", "tier": "INVALID", "reason": "This creator does not meet the region requirement. They are from Portugal, not the US or UK. Additionally, their content is focused on Portuguese history, not Conquerors Blade.", "match_score": 0, "content_tags": ["Portuguese History", "Age of Discovery", "Medieval Portugal", "European History", "Naval Battles"], "creatorMetrics": {"ins_id": "", "region": "PT", "language": "pt", "nickname": "Portuguese History 🇵🇹", "signature": "Portuguese History in English", "unique_id": "portuguesehistory", "twitter_id": "", "aweme_count": 12, "medianLikes": 139, "medianViews": 3941, "averageLikes": 168, "averageViews": 4256, "follower_count": 1146, "medianComments": 3, "averageComments": 4, "avgEngagementRate": 5.55, "youtube_channel_id": "", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@user84190518026006", "tier": "INVALID", "reason": "This creator does not meet the content type requirement. Their videos are focused on sports and gym content, not the game Conquerors Blade.", "match_score": 0, "content_tags": ["Sports", "Gym", "Fitness", "Motivation", "Workout"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "<PERSON>tra<PERSON><PERSON> <PERSON><PERSON>", "signature": "", "unique_id": "user84190518026006", "twitter_id": "", "aweme_count": 491, "medianLikes": 2, "medianViews": 746.5, "averageLikes": 283, "averageViews": 50350, "follower_count": 22647, "medianComments": 0, "averageComments": 5, "avgEngagementRate": 0.76, "youtube_channel_id": "", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@history.throughtime", "tier": "INVALID", "reason": "This creator does not meet the region requirement. They are from Switzerland, not the US or UK. Additionally, their content is focused on general medieval history, not specifically Conquerors Blade.", "match_score": 0, "content_tags": ["Medieval", "History", "Weird Facts", "Middle Ages", "Crazy History"], "creatorMetrics": {"ins_id": "", "region": "CH", "language": "de", "nickname": "history", "signature": "Create videos about historical events and aesthetics.", "unique_id": "history.throughtime", "twitter_id": "", "aweme_count": 50, "medianLikes": 29, "medianViews": 363, "averageLikes": 32, "averageViews": 371, "follower_count": 167, "medianComments": 2, "averageComments": 2, "avgEngagementRate": 9.72, "youtube_channel_id": "", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@grandeur_blade_odyssey", "tier": "INVALID", "reason": "This creator does not meet the region requirement. They are from Australia, not the US or UK. Additionally, their content is focused on general medieval history and Knights Templar, not specifically Conquerors Blade.", "match_score": 0, "content_tags": ["Knights Templar", "Medieval History", "Crusades", "History", "War History"], "creatorMetrics": {"ins_id": "", "region": "AU", "language": "zh-Hans", "nickname": "<PERSON>", "signature": "⬇️Interested in war themes? Subscribe to my new YouTube channel below! ⬇️", "unique_id": "grandeur_blade_odyssey", "twitter_id": "", "aweme_count": 195, "medianLikes": 234, "medianViews": 2835, "averageLikes": 3225, "averageViews": 68698, "follower_count": 20614, "medianComments": 5, "averageComments": 57, "avgEngagementRate": 8.49, "youtube_channel_id": "UC3A0Sl_-kwaFP3G4aegtYag", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@contries_histories", "tier": "INVALID", "reason": "This creator does not meet the region requirement. They are from Norway, not the US or UK. Additionally, their content is focused on general history, not specifically Conquerors Blade.", "match_score": 0, "content_tags": ["History", "Ottoman Empire", "Seljuk Turks", "Crusades", "World History"], "creatorMetrics": {"ins_id": "", "region": "NO", "language": "en", "nickname": "History", "signature": "Countries / History\nWe are not makers of history. We are made by history \n🏁 10k", "unique_id": "contries_histories", "twitter_id": "", "aweme_count": 964, "medianLikes": 36, "medianViews": 1478, "averageLikes": 965, "averageViews": 27561, "follower_count": 9955, "medianComments": 0, "averageComments": 27, "avgEngagementRate": 2.69, "youtube_channel_id": "", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@history_unboxed", "tier": "INVALID", "reason": "This creator does not meet the content type requirement. Their videos are focused on general history and medieval warfare, not specifically Conquerors Blade.", "match_score": 0, "content_tags": ["Medieval History", "Warfare", "History", "British History", "Crusades"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "History Unboxed", "signature": "🕰️ Welcome to History Unboxed - Unveiling the Past! 🌍", "unique_id": "history_unboxed", "twitter_id": "", "aweme_count": 98, "medianLikes": 116, "medianViews": 3333, "averageLikes": 1291, "averageViews": 42038, "follower_count": 2810, "medianComments": 2, "averageComments": 29, "avgEngagementRate": 2.98, "youtube_channel_id": "UCwFexbpU33ni-R6sflUkHtg", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@tods_workshop", "tier": "INVALID", "reason": "This creator does not meet the content type requirement. Their videos are focused on cars and general vlogging, not the game Conquerors Blade. The signature mentions 'medieval weapons' but recent videos do not reflect this.", "match_score": 0, "content_tags": ["Cars", "Classic Cars", "Garage", "Vlog", "Trending"], "creatorMetrics": {"ins_id": "", "region": "GB", "language": "en", "nickname": "Tod's Workshop", "signature": "<PERSON><PERSON>'s Workshop makes very accurate copies of medieval weapons, film props", "unique_id": "tods_workshop", "twitter_id": "", "aweme_count": 162, "medianLikes": 45.5, "medianViews": 1913, "averageLikes": 48, "averageViews": 13631, "follower_count": 14909, "medianComments": 1, "averageComments": 1, "avgEngagementRate": 1.93, "youtube_channel_id": "", "recentVideosCollected": 10}}, {"url": "https://www.tiktok.com/@truthbehindhistory", "tier": "INVALID", "reason": "This creator does not meet the content type requirement. Their videos are focused on general historical events and facts, not specifically Conquerors Blade.", "match_score": 0, "content_tags": ["History", "Hidden History", "Unsolved Mysteries", "Cultural Revolution", "American History"], "creatorMetrics": {"ins_id": "", "region": "US", "language": "en", "nickname": "TruthBehindHistory", "signature": "📚Educating the world one video at a time‼️", "unique_id": "truthbehindhistory", "twitter_id": "", "aweme_count": 798, "medianLikes": 30, "medianViews": 645, "averageLikes": 165, "averageViews": 6309, "follower_count": 5699, "medianComments": 0, "averageComments": 9, "avgEngagementRate": 4.08, "youtube_channel_id": "", "recentVideosCollected": 13}}, {"url": "https://www.tiktok.com/@insight.sphere5", "tier": "INVALID", "reason": "This creator does not meet the region requirement. They are from Malta, not the US or UK. Additionally, their content is focused on general history, specifically the Great Siege of Malta, not Conquerors Blade.", "match_score": 0, "content_tags": ["Great Siege of Malta", "History", "Knights of St. John", "Ottoman Empire", "Medieval History"], "creatorMetrics": {"ins_id": "", "region": "MT", "language": "en", "nickname": "Insight Sphere", "signature": "History, current affairs & fun facts in bite-sized videos 🌍 📚", "unique_id": "insight.sphere5", "twitter_id": "", "aweme_count": 10, "medianLikes": 161, "medianViews": 3214.5, "averageLikes": 286, "averageViews": 8271, "follower_count": 646, "medianComments": 3.5, "averageComments": 7, "avgEngagementRate": 4.53, "youtube_channel_id": "", "recentVideosCollected": 10}}]}