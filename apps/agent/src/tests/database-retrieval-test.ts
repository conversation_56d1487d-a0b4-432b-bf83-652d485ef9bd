import { getCreatorScoutResultsByContextId } from '@/workflows/creatorScoutWorkflow';
import { workflowDbService } from '@/services/index';

/**
 * Test database retrieval functionality
 */
async function testDatabaseRetrieval() {
  console.log('🧪 Testing Database Retrieval...\n');

  try {
    // Test 1: Check if workflowDbService is available
    console.log('📋 Test 1: Checking workflowDbService availability...');
    if (!workflowDbService) {
      console.error('❌ workflowDbService is not available');
      return;
    }
    console.log('✅ workflowDbService is available');

    // Test 2: Try to get a context by ID (using a test ID)
    console.log('\n📋 Test 2: Testing getContextById with test ID...');
    try {
      const testContext = await workflowDbService.getContextById(1);
      if (testContext) {
        console.log('✅ getContextById works, found context:', {
          id: testContext.id,
          contextType: testContext.contextType,
          traceId: testContext.traceId,
          hasData: !!testContext.contextData,
        });
      } else {
        console.log('ℹ️ No context found with ID 1 (this is normal if database is empty)');
      }
    } catch (error) {
      console.error('❌ Error testing getContextById:', error);
    }

    // Test 3: Test the helper function with a mock contextId
    console.log('\n📋 Test 3: Testing getCreatorScoutResultsByContextId...');
    try {
      const mockContextId = '1';
      const results = await getCreatorScoutResultsByContextId(mockContextId);
      if (results) {
        console.log('✅ getCreatorScoutResultsByContextId works, found results:', {
          hasResults: !!results.results,
          resultsCount: results.results?.length || 0,
          keys: Object.keys(results),
        });
      } else {
        console.log('ℹ️ No results found for contextId 1 (this is normal if database is empty)');
      }
    } catch (error) {
      console.error('❌ Error testing getCreatorScoutResultsByContextId:', error);
    }

    // Test 4: List recent workflow contexts to see what's available
    console.log('\n📋 Test 4: Checking recent workflow contexts...');
    try {
      // We'll need to check if there are any recent contexts
      // For now, let's just verify the database connection works
      console.log('ℹ️ Database connection test completed');
    } catch (error) {
      console.error('❌ Error checking recent contexts:', error);
    }

    console.log('\n✅ Database retrieval tests completed!');

  } catch (error) {
    console.error('❌ Database retrieval test failed:', error);
  }
}

/**
 * Test workflow result processing with mock data
 */
async function testWorkflowResultProcessing() {
  console.log('🧪 Testing Workflow Result Processing...\n');

  // Mock workflow result with contextId
  const mockWorkflowResult = {
    result: {
      scoutedCreators: 10,
      qualifiedCreators: 5,
      contextId: '123',
      filterSummary: {
        mode: 'LOOSE',
        total_analyzed: 10,
        total_qualified: 5,
        tier_breakdown: {
          PERFECT: 2,
          EXCELLENT: 2,
          GOOD: 1,
          ACCEPTABLE: 0,
        },
      },
    },
  };

  console.log('📊 Mock workflow result:', mockWorkflowResult);

  // Test the result processing logic
  try {
    const { getCreatorScoutResultsByContextId } = await import('@/workflows/creatorScoutWorkflow');
    
    console.log('\n📋 Testing contextId retrieval...');
    const dbResults = await getCreatorScoutResultsByContextId(mockWorkflowResult.result.contextId);
    
    if (dbResults) {
      console.log('✅ Successfully retrieved results from database');
      console.log('📊 Database results structure:', Object.keys(dbResults));
    } else {
      console.log('ℹ️ No results found (expected for mock contextId)');
    }

  } catch (error) {
    console.error('❌ Error testing workflow result processing:', error);
  }
}

/**
 * Main test function
 */
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';

  console.log('🧪 Database Retrieval Tests');
  console.log('===========================\n');

  try {
    switch (testType) {
      case 'db':
        await testDatabaseRetrieval();
        break;
        
      case 'processing':
        await testWorkflowResultProcessing();
        break;
        
      case 'all':
        await testDatabaseRetrieval();
        console.log('\n' + '='.repeat(50) + '\n');
        await testWorkflowResultProcessing();
        break;
        
      default:
        console.log('Available test types:');
        console.log('  db         - Test database retrieval functions');
        console.log('  processing - Test workflow result processing');
        console.log('  all        - Run all tests');
        console.log('\nUsage: npm run test:db [test-type]');
        break;
    }
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export {
  testDatabaseRetrieval,
  testWorkflowResultProcessing,
};
