import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { <PERSON><PERSON> } from '@mastra/core';
import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { select } from '@inquirer/prompts';

// Register the workflow and agents
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { creatorScoutWorkflow },
});

const conquerorBladeCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'conquerors-blade',
  campaignName: 'Conquerors Blade Campaign',
  description:
    'Find English speaking KOLs from US or UK who post about Conquerors Blade',

  // Campaign-level targets
  targetKOLCount: 300, // Total KOLs needed across all workflows
  kolPerTask: 80, // KOLs per individual workflow run
  maxWorkflowRuns: 10, // Maximum workflow runs to prevent infinite loops

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Conqueror's Blade gameplay, tutorials, and guides
        - Conqueror's Blade unit reviews and tier lists
        - Conqueror's Blade battle tactics and strategy content
        - Conqueror's Blade season updates and patch reviews
        - Conqueror's Blade house/clan content and events
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Primary focus on Conqueror's Blade content
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results/conquerors-blade',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run
};

const mountNBladeCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'mount-n-blade',
  campaignName: 'Mount & Blade Campaign',
  description:
    'Find English speaking KOLs from US or Europe who create Mount & Blade content and related medieval gaming content',

  // Campaign-level targets
  targetKOLCount: 300, // Total KOLs needed across all workflows
  kolPerTask: 80, // KOLs per individual workflow run
  maxWorkflowRuns: 10, // Maximum workflow runs to prevent infinite loops

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Mount & Blade series content (Mount & Blade, Warband, Bannerlord)
        - Mount & Blade gameplay, tutorials, reviews, and guides
        - Mount & Blade mods and modding content
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Primary focus on Mount & Blade series
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results/mount-n-blade',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run
};

const coldWeaponsCampaignConfig: CampaignConfig = {
  // Campaign identification
  campaignId: 'cold-weapons-war-culture',
  campaignName: 'Cold Weapons & War Culture Campaign',
  description:
    'Find Chinese speaking KOLs who post about cold weapons, ancient warfare, and war culture',

  // Campaign-level targets
  targetKOLCount: 300, // Total KOLs needed across all workflows
  kolPerTask: 80, // KOLs per individual workflow run
  maxWorkflowRuns: 10, // Maximum workflow runs to prevent infinite loops

  // Shared workflow configuration
  sharedConfig: {
    targetCreatorDescription: `Find me some KOLs:
      - Ethnicity: English speaking creators only, from US or Europe, etc.
      - Creator size: Not specified
      - Content Type:
        - Cold weapons enthusiasts (swords, knives, medieval weapons, traditional weapons)
        - Ancient warfare and military history
        - War culture and military traditions
        - Historical battles and tactics
        - Weapon crafting and blacksmithing
        - HEMA (Historical European Martial Arts) practitioners
        - Traditional martial arts with weapons focus
      - Constraints:
        - Must be English speaking
        - Must be from US or Europe
        - Content should focus on cold weapons, ancient warfare, or war culture
        - Educational or enthusiast content preferred
        - Historical accuracy and craftsmanship appreciation
        `,
    useIntelligentChallengeSelection: true,
    filterMode: 'STRICT' as const,
    pickerMode: 'STRATEGIC' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 0,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,
    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  },

  // Campaign execution settings
  concurrentTasksLimit: 4, // Respect rate limits
  persistenceType: 'json',
  outputDirectory: './campaign-results/cold-weapons-war-culture',

  // Progressive reporting settings
  enableProgressiveReporting: true,
  reportingInterval: 1, // Report after every workflow run
};

/**
 * Demo function for Scout Campaign feature
 */
async function runScoutCampaignDemo(campaignConfig: CampaignConfig) {
  console.log('🚀 Starting Scout Campaign Demo...\n');

  try {
    // Create and run the campaign
    const campaign = new ScoutCampaign(campaignConfig, mastra);

    console.log('📋 Campaign Configuration:');
    console.log(`   🎯 Target KOLs: ${campaignConfig.targetKOLCount}`);
    console.log(`   📊 KOLs per task: ${campaignConfig.kolPerTask}`);
    console.log(`   🔄 Max workflow runs: ${campaignConfig.maxWorkflowRuns}`);
    console.log(`   💾 Output directory: ${campaignConfig.outputDirectory}`);
    console.log(`   🔧 Filter mode: ${campaignConfig.sharedConfig.filterMode}`);
    console.log(`   🏁 Picker mode: ${campaignConfig.sharedConfig.pickerMode}`);
    if (campaignConfig.sharedConfig.minViews > 0) {
      console.log(`   👀 Min views: ${campaignConfig.sharedConfig.minViews}`);
    }
    if (campaignConfig.sharedConfig.minLikes > 0) {
      console.log(`   💖 Min likes: ${campaignConfig.sharedConfig.minLikes}`);
    }
    if (campaignConfig.sharedConfig.minComments > 0) {
      console.log(
        `   💬 Min comments: ${campaignConfig.sharedConfig.minComments}`,
      );
    }
    if (campaignConfig.sharedConfig.minFollowers > 0) {
      console.log(
        `   👥 Min followers: ${campaignConfig.sharedConfig.minFollowers}`,
      );
    }
    if (campaignConfig.sharedConfig.minRecentMedianViews > 0) {
      console.log(
        `   📈 Min median views: ${campaignConfig.sharedConfig.minRecentMedianViews}`,
      );
    }
    if (campaignConfig.sharedConfig.minRecentMedianLikes > 0) {
      console.log(
        `   📈 Min median likes: ${campaignConfig.sharedConfig.minRecentMedianLikes}`,
      );
    }
    if (campaignConfig.sharedConfig.minRecentMedianComments > 0) {
      console.log(
        `   📈 Min median comments: ${campaignConfig.sharedConfig.minRecentMedianComments}`,
      );
    }
    console.log('');

    // Run the campaign
    const campaignResults = await campaign.runCampaign();

    console.log('\n🎉 Campaign completed successfully!');
    console.log(`📊 Total batches processed: ${campaignResults.length}`);

    // Display summary of each batch
    campaignResults.forEach((batch) => {
      console.log(`\n📋 Batch ${batch.batchNumber} Summary:`);
      console.log(`   🆕 New unique KOLs: ${batch.newUniqueKOLs}`);
      console.log(`   📊 Total unique KOLs: ${batch.totalUniqueKOLs}`);
      console.log(
        `   ⏱️ Execution time: ${formatDuration(batch.executionTime)}`,
      );
      console.log(`   📅 Timestamp: ${batch.timestamp}`);
    });

    // Display final campaign status
    const finalStatus = campaign.getCampaignStatus();
    console.log('\n📈 Final Campaign Statistics:');
    console.log(
      `   🎯 Total unique KOLs found: ${finalStatus.totalUniqueKOLs}`,
    );
    console.log(
      `   📊 Total scouted results: ${finalStatus.totalScoutedResults}`,
    );
    console.log(
      `   🔄 Workflow runs completed: ${finalStatus.workflowRunsCompleted}`,
    );
    console.log(
      `   ✅ Successful runs: ${finalStatus.statistics.successfulRuns}`,
    );
    console.log(`   ❌ Failed runs: ${finalStatus.statistics.failedRuns}`);
    console.log(
      `   📈 Average KOLs per run: ${finalStatus.statistics.averageKOLsPerRun.toFixed(1)}`,
    );
    console.log(
      `   ⏱️ Total execution time: ${formatDuration(finalStatus.statistics.totalExecutionTime)}`,
    );
  } catch (error) {
    console.error('❌ Campaign failed:', error);
    process.exit(1);
  }
}

/**
 * Utility function to format duration
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Main function to run different demo scenarios
 */
async function main() {
  const demoType = await select({
    message: 'Select demo type:',
    choices: [
      { value: 'conquerorsBlade', name: 'Conquerors Blade Campaign' },
      { value: 'mountNBlade', name: 'Mount & Blade Campaign' },
      { value: 'coldWeapons', name: 'Cold Weapons & War Culture Campaign' },
    ],
  });

  console.log('🎯 Scout Campaign Demo System');
  console.log('============================\n');

  switch (demoType) {
    case 'conquerorsBlade':
      console.log('Running Conquerors Blade campaign...');
      await runScoutCampaignDemo(conquerorBladeCampaignConfig);
      break;

    case 'mountNBlade':
      console.log('Running Mount & Blade campaign...');
      await runScoutCampaignDemo(mountNBladeCampaignConfig);
      break;

    case 'coldWeapons':
      console.log('Running Cold Weapons & War Culture campaign...');
      await runScoutCampaignDemo(coldWeaponsCampaignConfig);
      break;

    default:
      break;
  }
}

// Run the demo if this file is executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export { runScoutCampaignDemo, formatDuration };
