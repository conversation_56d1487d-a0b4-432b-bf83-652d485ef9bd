import { ScoutCampaign, CampaignConfig } from '@/campaigns/ScoutCampaign';
import { createCampaignFromTemplate, getTemplate } from '@/campaigns/templates';
import { Mastra } from '@mastra/core';
import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';

// Register the workflow and agents
const mastra = new Mastra({
  agents: { creatorHashtagScout, creatorFilterAgent, challengePickerAgent },
  workflows: { creatorScoutWorkflow },
});

/**
 * Test deep deduplication integration between campaign and workflow
 */
async function testDeepDeduplication() {
  console.log('🧪 Testing Deep Deduplication Integration...\n');

  // Create a small test campaign to verify deduplication
  const testCampaignConfig = createCampaignFromTemplate(getTemplate('test'), {
    campaignId: 'deep-dedup-test',
    campaignName: 'Deep Deduplication Test',
    description: 'Test campaign to verify deep deduplication integration',
    targetKOLCount: 20, // Small target for testing
    kolPerTask: 10, // Small batch size
    maxWorkflowRuns: 3, // Run multiple times to test deduplication
    outputDirectory: './campaign-results/deep-dedup-test',
  });

  console.log('📋 Test Campaign Configuration:');
  console.log(`   🎯 Target KOLs: ${testCampaignConfig.targetKOLCount}`);
  console.log(`   📊 KOLs per task: ${testCampaignConfig.kolPerTask}`);
  console.log(`   🔄 Max workflow runs: ${testCampaignConfig.maxWorkflowRuns}`);
  console.log(`   📁 Output directory: ${testCampaignConfig.outputDirectory}`);
  console.log('');

  try {
    const campaign = new ScoutCampaign(testCampaignConfig, mastra);

    console.log('🚀 Starting campaign with deep deduplication...');
    const results = await campaign.runCampaign();

    console.log('\n✅ Deep deduplication test completed!');
    console.log(`📊 Total batches processed: ${results.length}`);

    // Analyze deduplication effectiveness
    const finalStatus = campaign.getCampaignStatus();
    console.log('\n📈 Deduplication Analysis:');
    console.log(
      `   🎯 Total unique KOLs found: ${finalStatus.totalUniqueKOLs}`,
    );
    console.log(
      `   📊 Total scouted results: ${finalStatus.totalScoutedResults}`,
    );
    console.log(
      `   🔄 Workflow runs completed: ${finalStatus.workflowRunsCompleted}`,
    );
    console.log(
      `   📹 Total videos tracked: ${finalStatus.scoutedVideoIds.length}`,
    );
    console.log(
      `   👥 Total creators tracked: ${finalStatus.scoutedCreatorIds.length}`,
    );

    // Calculate deduplication efficiency
    const avgResultsPerRun =
      finalStatus.totalScoutedResults / finalStatus.workflowRunsCompleted;
    const avgUniquePerRun =
      finalStatus.totalUniqueKOLs / finalStatus.workflowRunsCompleted;
    const deduplicationRate =
      ((avgResultsPerRun - avgUniquePerRun) / avgResultsPerRun) * 100;

    console.log(
      `   📈 Average results per run: ${avgResultsPerRun.toFixed(1)}`,
    );
    console.log(`   📈 Average unique per run: ${avgUniquePerRun.toFixed(1)}`);
    console.log(`   🔄 Deduplication rate: ${deduplicationRate.toFixed(1)}%`);

    // Display batch-by-batch results
    console.log('\n📊 Batch-by-Batch Results:');
    results.forEach((batch, index) => {
      console.log(`   Batch ${batch.batchNumber}:`);
      console.log(`     🆕 New unique KOLs: ${batch.newUniqueKOLs}`);
      console.log(`     📊 Total unique KOLs: ${batch.totalUniqueKOLs}`);
      console.log(
        `     ⏱️ Execution time: ${formatDuration(batch.executionTime)}`,
      );
      console.log(`     📅 Timestamp: ${batch.timestamp}`);
    });

    // Verify deduplication worked
    if (finalStatus.workflowRunsCompleted > 1) {
      const lastBatch = results[results.length - 1];
      if (lastBatch.newUniqueKOLs < testCampaignConfig.kolPerTask) {
        console.log(
          '\n✅ Deduplication is working! Later batches found fewer new unique KOLs.',
        );
      } else {
        console.log(
          '\n⚠️ Deduplication may not be working optimally. All batches found similar numbers of unique KOLs.',
        );
      }
    }
  } catch (error) {
    console.error('❌ Deep deduplication test failed:', error);
    throw error;
  }
}

/**
 * Test single workflow with campaign parameters
 */
async function testSingleWorkflowWithCampaignParams() {
  console.log('🧪 Testing Single Workflow with Campaign Parameters...\n');

  const registeredWorkflow = mastra.getWorkflow('creatorScoutWorkflow');
  const run = registeredWorkflow.createRun();

  // Test input data with campaign parameters
  const inputData = {
    targetCreatorDescription: 'Find gaming creators for testing deduplication',
    useIntelligentChallengeSelection: false, // Faster for testing
    desiredCreatorCount: 5, // Small number for testing
    filterMode: 'LOOSE' as const,
    minViews: 0,
    minLikes: 0,
    minComments: 0,
    minFollowers: 1000,
    minRecentMedianViews: 0,
    minRecentMedianComments: 0,
    minRecentMedianLikes: 0,

    // Campaign mode parameters
    campaignMode: true,
    batchNumber: 1,
    skipVideoIds: ['test_video_1', 'test_video_2'], // Mock skip list
    skipCreatorIds: ['test_creator_1', 'test_creator_2'], // Mock skip list
    sequentialCursorMode: true,

    // Image processing parameters
    uploadToOss: false,
    downloadThumbnailAsBuffer: false,
  };

  console.log('📝 Testing workflow with campaign parameters:');
  console.log(`   🎯 Campaign mode: ${inputData.campaignMode}`);
  console.log(`   📊 Batch number: ${inputData.batchNumber}`);
  console.log(`   🚫 Skip video IDs: ${inputData.skipVideoIds.length}`);
  console.log(`   🚫 Skip creator IDs: ${inputData.skipCreatorIds.length}`);
  console.log(
    `   📊 Sequential cursor mode: ${inputData.sequentialCursorMode}`,
  );
  console.log('');

  try {
    const result = await run.start({ inputData });

    console.log('✅ Single workflow with campaign parameters completed!');
    console.log(`📊 Result type: ${typeof result.result}`);

    if (result.result?.contextId) {
      console.log(
        `📋 Results stored with contextId: ${result.result.contextId}`,
      );
    }
  } catch (error) {
    console.error('❌ Single workflow test failed:', error);
    throw error;
  }
}

/**
 * Utility function to format duration
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * Main test function
 */
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'campaign';

  console.log('🧪 Deep Deduplication Integration Tests');
  console.log('=====================================\n');

  try {
    switch (testType) {
      case 'campaign':
        console.log('Running campaign deduplication test...');
        await testDeepDeduplication();
        break;

      case 'workflow':
        console.log('Running single workflow test...');
        await testSingleWorkflowWithCampaignParams();
        break;

      case 'both':
        console.log('Running both tests...');
        await testSingleWorkflowWithCampaignParams();
        console.log('\n' + '='.repeat(50) + '\n');
        await testDeepDeduplication();
        break;

      default:
        console.log('Available test types:');
        console.log('  campaign  - Test campaign deduplication');
        console.log('  workflow  - Test single workflow with campaign params');
        console.log('  both      - Run both tests');
        console.log('\nUsage: npm run test:dedup [test-type]');
        break;
    }

    console.log('\n🎉 All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

export {
  testDeepDeduplication,
  testSingleWorkflowWithCampaignParams,
  formatDuration,
};
